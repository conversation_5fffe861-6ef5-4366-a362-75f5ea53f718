"""
Sentry configuration and error tracking for Lo-Fi Video Generator.
"""

import os
import logging
import platform
import sys
from typing import Optional, Dict, Any
from contextlib import contextmanager

import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration
from sentry_sdk.integrations.asyncio import AsyncioIntegration

logger = logging.getLogger(__name__)

# Global flag to track if Sentry is initialized
_sentry_initialized = False


def configure_sentry(
    dsn: Optional[str] = None,
    environment: Optional[str] = None,
    release: Optional[str] = None,
    sample_rate: float = 1.0,
    traces_sample_rate: float = 0.1,
    debug: bool = False
) -> bool:
    """
    Configure Sentry error tracking and performance monitoring.
    
    Args:
        dsn: Sentry DSN (Data Source Name)
        environment: Environment name (development, production, etc.)
        release: Release version
        sample_rate: Error sampling rate (0.0 to 1.0)
        traces_sample_rate: Performance monitoring sampling rate (0.0 to 1.0)
        debug: Enable debug mode
        
    Returns:
        bool: True if Sentry was successfully configured
    """
    global _sentry_initialized
    
    if _sentry_initialized:
        return True
    
    # Get configuration from environment variables
    dsn = dsn or os.getenv("SENTRY_DSN")
    environment = environment or os.getenv("SENTRY_ENVIRONMENT", _detect_environment())
    release = release or os.getenv("SENTRY_RELEASE", "0.1.0")
    
    # Check if error tracking is enabled
    if not os.getenv("ENABLE_ERROR_TRACKING", "true").lower() == "true":
        logger.debug("Error tracking disabled via ENABLE_ERROR_TRACKING")
        return False
    
    if not dsn:
        logger.debug("No Sentry DSN provided, error tracking disabled")
        return False
    
    # Don't send errors in test environment
    if environment == "testing":
        logger.debug("Test environment detected, error tracking disabled")
        return False
    
    try:
        # Configure logging integration
        logging_integration = LoggingIntegration(
            level=logging.INFO,        # Capture info and above as breadcrumbs
            event_level=logging.ERROR  # Send errors as events
        )
        
        # Configure asyncio integration
        asyncio_integration = AsyncioIntegration()
        
        # Initialize Sentry
        sentry_sdk.init(
            dsn=dsn,
            environment=environment,
            release=release,
            sample_rate=sample_rate,
            traces_sample_rate=traces_sample_rate,
            debug=debug,
            integrations=[logging_integration, asyncio_integration],
            before_send=_before_send_filter,
            attach_stacktrace=True,
            send_default_pii=False,  # Don't send personally identifiable information
        )
        
        # Set global tags
        sentry_sdk.set_tag("component", "lofi-video-generator")
        sentry_sdk.set_tag("platform", platform.system().lower())
        sentry_sdk.set_tag("python_version", f"{sys.version_info.major}.{sys.version_info.minor}")
        
        # Set user context (non-PII)
        sentry_sdk.set_user({
            "id": "anonymous",
            "platform": platform.system(),
            "python_version": sys.version
        })
        
        _sentry_initialized = True
        logger.info(f"Sentry initialized for environment: {environment}")
        
        # Send a test message to verify configuration
        if debug:
            capture_message("Sentry configuration test", level="info")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize Sentry: {e}")
        return False


def _detect_environment() -> str:
    """Detect the current environment."""
    if os.getenv("CI"):
        return "ci"
    elif os.getenv("PYTEST_CURRENT_TEST"):
        return "testing"
    elif os.getenv("SENTRY_ENVIRONMENT"):
        return os.getenv("SENTRY_ENVIRONMENT")
    else:
        return "development"


def _before_send_filter(event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Filter sensitive information before sending to Sentry.
    
    Args:
        event: Sentry event data
        hint: Additional context
        
    Returns:
        Filtered event or None to drop the event
    """
    # Remove sensitive information from URLs
    if "request" in event:
        request = event["request"]
        if "url" in request:
            url = request["url"]
            # Remove API keys from URLs
            if "token=" in url:
                request["url"] = url.split("token=")[0] + "token=***"
            if "api_key=" in url:
                request["url"] = url.split("api_key=")[0] + "api_key=***"
    
    # Filter out sensitive environment variables
    if "contexts" in event and "runtime" in event["contexts"]:
        runtime = event["contexts"]["runtime"]
        if "env" in runtime:
            env = runtime["env"]
            sensitive_keys = ["FREESOUND_API_KEY", "PIXABAY_API_KEY", "MUBERT_API_KEY", "SENTRY_DSN"]
            for key in sensitive_keys:
                if key in env:
                    env[key] = "***"
    
    return event


def capture_exception(
    exception: Exception,
    context: Optional[Dict[str, Any]] = None,
    tags: Optional[Dict[str, str]] = None,
    level: str = "error"
) -> None:
    """
    Capture an exception with additional context.
    
    Args:
        exception: The exception to capture
        context: Additional context data
        tags: Tags to add to the event
        level: Severity level
    """
    if not _sentry_initialized:
        return
    
    with sentry_sdk.push_scope() as scope:
        if context:
            for key, value in context.items():
                scope.set_context(key, value)
        
        if tags:
            for key, value in tags.items():
                scope.set_tag(key, value)
        
        scope.level = level
        sentry_sdk.capture_exception(exception)


def capture_message(
    message: str,
    level: str = "info",
    tags: Optional[Dict[str, str]] = None,
    context: Optional[Dict[str, Any]] = None
) -> None:
    """
    Capture a custom message.
    
    Args:
        message: Message to capture
        level: Severity level
        tags: Tags to add to the event
        context: Additional context data
    """
    if not _sentry_initialized:
        return
    
    with sentry_sdk.push_scope() as scope:
        if context:
            for key, value in context.items():
                scope.set_context(key, value)
        
        if tags:
            for key, value in tags.items():
                scope.set_tag(key, value)
        
        scope.level = level
        sentry_sdk.capture_message(message)


def add_breadcrumb(
    message: str,
    category: str = "default",
    level: str = "info",
    data: Optional[Dict[str, Any]] = None
) -> None:
    """
    Add a breadcrumb for debugging.
    
    Args:
        message: Breadcrumb message
        category: Category of the breadcrumb
        level: Severity level
        data: Additional data
    """
    if not _sentry_initialized:
        return
    
    sentry_sdk.add_breadcrumb(
        message=message,
        category=category,
        level=level,
        data=data or {}
    )


@contextmanager
def SentryTransaction(name: str, op: str = "task"):
    """
    Context manager for performance monitoring.
    
    Args:
        name: Transaction name
        op: Operation type
    """
    if not _sentry_initialized:
        yield
        return
    
    with sentry_sdk.start_transaction(name=name, op=op) as transaction:
        yield transaction


def set_user_context(user_data: Dict[str, Any]) -> None:
    """
    Set user context for error tracking.
    
    Args:
        user_data: User context data (should not contain PII)
    """
    if not _sentry_initialized:
        return
    
    sentry_sdk.set_user(user_data)


def set_operation_context(operation: str, **kwargs) -> None:
    """
    Set context for the current operation.
    
    Args:
        operation: Operation name
        **kwargs: Additional context data
    """
    if not _sentry_initialized:
        return
    
    sentry_sdk.set_tag("operation", operation)
    
    if kwargs:
        sentry_sdk.set_context("operation_data", kwargs)
