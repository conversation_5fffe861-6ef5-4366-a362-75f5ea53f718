"""
Audio processing module for video generation.
Handles audio concatenation, normalization, and optimization.
"""

import asyncio
import subprocess
import tempfile
from pathlib import Path
from typing import List, Optional, Tuple
import logging

from .sentry_config import capture_exception, add_breadcrumb, SentryTransaction
from .music_sources.base import Track

logger = logging.getLogger(__name__)


class AudioProcessor:
    """Handles audio processing for video generation."""
    
    def __init__(self, temp_dir: Optional[Path] = None):
        self.temp_dir = temp_dir or Path(tempfile.gettempdir()) / "lofi_audio"
        self.temp_dir.mkdir(exist_ok=True)
    
    async def process_single_audio(
        self,
        audio_data: bytes,
        track: Track,
        target_duration: float
    ) -> Optional[Path]:
        """
        Process single audio track for video generation.
        
        Args:
            audio_data: Raw audio data
            track: Track metadata
            target_duration: Target duration in seconds
            
        Returns:
            Path to processed audio file
        """
        add_breadcrumb(
            "Processing single audio track",
            category="audio",
            data={
                "track_id": track.id,
                "track_duration": track.duration,
                "target_duration": target_duration,
                "audio_size": len(audio_data)
            }
        )
        
        try:
            with SentryTransaction("process_single_audio", "audio_processing"):
                # Create temporary audio file
                audio_file = self.temp_dir / f"audio_{track.id}.mp3"
                
                with open(audio_file, 'wb') as f:
                    f.write(audio_data)
                
                # Process audio (normalize, trim/loop as needed)
                processed_file = await self._process_audio_file(
                    audio_file, target_duration, f"processed_{track.id}"
                )
                
                # Clean up original temp file
                if audio_file.exists():
                    audio_file.unlink()
                
                return processed_file
                
        except Exception as e:
            capture_exception(
                e,
                context={
                    "operation": "single_audio_processing",
                    "track_id": track.id,
                    "target_duration": target_duration
                }
            )
            logger.error(f"Single audio processing failed: {e}")
            return None
    
    async def process_multiple_audio(
        self,
        audio_data_list: List[bytes],
        tracks: List[Track],
        target_duration: float
    ) -> Optional[Path]:
        """
        Process and concatenate multiple audio tracks.
        
        Args:
            audio_data_list: List of raw audio data
            tracks: List of track metadata
            target_duration: Target duration in seconds
            
        Returns:
            Path to concatenated audio file
        """
        add_breadcrumb(
            "Processing multiple audio tracks",
            category="audio",
            data={
                "track_count": len(tracks),
                "target_duration": target_duration,
                "total_audio_size": sum(len(data) for data in audio_data_list)
            }
        )
        
        try:
            with SentryTransaction("process_multiple_audio", "audio_processing"):
                # Create temporary audio files
                temp_audio_files = []
                
                for i, (audio_data, track) in enumerate(zip(audio_data_list, tracks)):
                    audio_file = self.temp_dir / f"audio_{track.id}_{i}.mp3"
                    
                    with open(audio_file, 'wb') as f:
                        f.write(audio_data)
                    
                    temp_audio_files.append(audio_file)
                
                # Concatenate audio files
                concatenated_file = await self._concatenate_audio_files(
                    temp_audio_files, target_duration
                )
                
                # Clean up temporary files
                for audio_file in temp_audio_files:
                    if audio_file.exists():
                        audio_file.unlink()
                
                return concatenated_file
                
        except Exception as e:
            capture_exception(
                e,
                context={
                    "operation": "multiple_audio_processing",
                    "track_count": len(tracks),
                    "target_duration": target_duration
                }
            )
            logger.error(f"Multiple audio processing failed: {e}")
            return None
    
    async def _process_audio_file(
        self,
        input_file: Path,
        target_duration: float,
        output_name: str
    ) -> Optional[Path]:
        """Process audio file with normalization and duration adjustment."""
        output_file = self.temp_dir / f"{output_name}_processed.mp3"
        
        # Get audio duration first
        duration = await self._get_audio_duration(input_file)
        if duration is None:
            return None
        
        # Determine if we need to loop or trim
        if duration < target_duration:
            # Loop audio to reach target duration
            loop_count = int(target_duration / duration) + 1
            cmd = [
                'ffmpeg', '-y',
                '-stream_loop', str(loop_count),
                '-i', str(input_file),
                '-t', str(target_duration),
                '-af', 'loudnorm=I=-16:TP=-1.5:LRA=11',  # Normalize audio
                '-c:a', 'mp3',
                '-b:a', '192k',
                str(output_file)
            ]
        else:
            # Trim audio to target duration
            cmd = [
                'ffmpeg', '-y',
                '-i', str(input_file),
                '-t', str(target_duration),
                '-af', 'loudnorm=I=-16:TP=-1.5:LRA=11',  # Normalize audio
                '-c:a', 'mp3',
                '-b:a', '192k',
                str(output_file)
            ]
        
        return await self._run_ffmpeg_command(cmd, output_file)
    
    async def _concatenate_audio_files(
        self,
        audio_files: List[Path],
        target_duration: float
    ) -> Optional[Path]:
        """Concatenate multiple audio files."""
        output_file = self.temp_dir / f"concatenated_{len(audio_files)}_tracks.mp3"
        
        # Build FFmpeg command for concatenation
        cmd = ['ffmpeg', '-y']
        
        # Add input files
        for audio_file in audio_files:
            cmd.extend(['-i', str(audio_file)])
        
        # Create filter complex for concatenation with crossfade
        if len(audio_files) == 1:
            # Single file, just process normally
            cmd.extend([
                '-t', str(target_duration),
                '-af', 'loudnorm=I=-16:TP=-1.5:LRA=11',
                '-c:a', 'mp3',
                '-b:a', '192k',
                str(output_file)
            ])
        else:
            # Multiple files, concatenate with crossfade
            filter_inputs = []
            filter_chain = []
            
            for i in range(len(audio_files)):
                filter_inputs.append(f'[{i}:0]')
            
            # Create crossfade chain
            current_output = filter_inputs[0]
            for i in range(1, len(audio_files)):
                next_input = filter_inputs[i]
                crossfade_output = f'[cf{i}]'
                filter_chain.append(f'{current_output}{next_input}acrossfade=d=2{crossfade_output}')
                current_output = crossfade_output
            
            # Add normalization and duration limit
            filter_chain.append(f'{current_output}loudnorm=I=-16:TP=-1.5:LRA=11[out]')
            
            filter_complex = ';'.join(filter_chain)
            
            cmd.extend([
                '-filter_complex', filter_complex,
                '-map', '[out]',
                '-t', str(target_duration),
                '-c:a', 'mp3',
                '-b:a', '192k',
                str(output_file)
            ])
        
        return await self._run_ffmpeg_command(cmd, output_file)
    
    async def _get_audio_duration(self, audio_file: Path) -> Optional[float]:
        """Get duration of audio file in seconds."""
        cmd = [
            'ffprobe', '-v', 'quiet',
            '-show_entries', 'format=duration',
            '-of', 'csv=p=0',
            str(audio_file)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                duration_str = stdout.decode().strip()
                return float(duration_str)
            else:
                logger.error(f"FFprobe error: {stderr.decode()}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting audio duration: {e}")
            return None
    
    async def _run_ffmpeg_command(self, cmd: List[str], output_path: Path) -> Optional[Path]:
        """Run FFmpeg command and return output path on success."""
        try:
            add_breadcrumb(
                "Running FFmpeg audio command",
                category="audio",
                data={"command": " ".join(cmd[:5]) + "..."}  # Log first few args
            )
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and output_path.exists():
                add_breadcrumb(
                    "FFmpeg audio command successful",
                    category="audio",
                    data={"output_size": output_path.stat().st_size}
                )
                return output_path
            else:
                logger.error(f"FFmpeg audio error: {stderr.decode()}")
                return None
                
        except Exception as e:
            logger.error(f"Error running FFmpeg audio command: {e}")
            return None
    
    def cleanup_temp_files(self) -> None:
        """Clean up temporary audio files."""
        try:
            for file_path in self.temp_dir.glob("*"):
                if file_path.is_file():
                    file_path.unlink()
            add_breadcrumb("Audio temp files cleaned up", category="cleanup")
        except Exception as e:
            logger.warning(f"Error cleaning up audio temp files: {e}")
    
    async def get_audio_info(self, audio_file: Path) -> Optional[dict]:
        """Get detailed audio information."""
        cmd = [
            'ffprobe', '-v', 'quiet',
            '-print_format', 'json',
            '-show_format', '-show_streams',
            str(audio_file)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                import json
                return json.loads(stdout.decode())
            else:
                logger.error(f"FFprobe info error: {stderr.decode()}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting audio info: {e}")
            return None
