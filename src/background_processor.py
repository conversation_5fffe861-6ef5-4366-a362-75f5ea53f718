"""
Background processing module for video generation.
Handles various background formats including GIF, APNG, WEBP, MNG, MP4, WebM.
"""

import asyncio
import subprocess
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any, List
import logging

from .sentry_config import capture_exception, add_breadcrumb, SentryTransaction
from .music_sources.base import LoFiStyle

logger = logging.getLogger(__name__)


class BackgroundProcessor:
    """Handles background generation and processing for video generation."""
    
    # Supported background formats
    SUPPORTED_FORMATS = {
        'gif', 'apng', 'webp', 'mng', 'mp4', 'webm', 'mov', 'avi'
    }
    
    # Style-specific visual configurations
    STYLE_VISUALS = {
        LoFiStyle.UPBEAT: {
            'colors': ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
            'animation': 'energetic',
            'elements': ['particles', 'waves', 'geometric']
        },
        LoFiStyle.CALMING: {
            'colors': ['#A8E6CF', '#DCEDC1', '#FFD3A5', '#FD9853'],
            'animation': 'gentle',
            'elements': ['clouds', 'water', 'nature']
        },
        LoFiStyle.CHILL: {
            'colors': ['#667eea', '#764ba2', '#f093fb', '#f5576c'],
            'animation': 'smooth',
            'elements': ['gradients', 'soft_shapes', 'minimal']
        },
        LoFiStyle.DREAMY: {
            'colors': ['#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'],
            'animation': 'floating',
            'elements': ['stars', 'mist', 'ethereal']
        },
        LoFiStyle.NOSTALGIC: {
            'colors': ['#d299c2', '#fef9d7', '#dee5d4', '#efd3d7'],
            'animation': 'vintage',
            'elements': ['film_grain', 'sepia', 'retro']
        },
        LoFiStyle.FOCUS: {
            'colors': ['#667eea', '#764ba2', '#89f7fe', '#66a6ff'],
            'animation': 'steady',
            'elements': ['lines', 'focus_points', 'clean']
        },
        LoFiStyle.STUDY: {
            'colors': ['#a8edea', '#fed6e3', '#d299c2', '#fef9d7'],
            'animation': 'calm',
            'elements': ['books', 'desk', 'minimal']
        },
        LoFiStyle.RELAXING: {
            'colors': ['#ffecd2', '#fcb69f', '#667eea', '#764ba2'],
            'animation': 'slow',
            'elements': ['nature', 'zen', 'peaceful']
        },
        LoFiStyle.AMBIENT: {
            'colors': ['#667eea', '#764ba2', '#89f7fe', '#66a6ff'],
            'animation': 'atmospheric',
            'elements': ['space', 'ambient', 'abstract']
        },
        LoFiStyle.JAZZY: {
            'colors': ['#8B4513', '#A0522D', '#CD853F', '#D2691E'],
            'animation': 'rhythmic',
            'elements': ['instruments', 'notes', 'vintage']
        }
    }
    
    def __init__(self, temp_dir: Optional[Path] = None):
        self.temp_dir = temp_dir or Path(tempfile.gettempdir()) / "lofi_backgrounds"
        self.temp_dir.mkdir(exist_ok=True)
    
    async def process_background(
        self,
        background_path: Optional[Path],
        style: LoFiStyle,
        duration: float,
        resolution: str = "1920x1080"
    ) -> Optional[Path]:
        """
        Process background for video generation.
        
        Args:
            background_path: Path to custom background file (optional)
            style: Lo-fi style for generated background
            duration: Video duration in seconds
            resolution: Video resolution
            
        Returns:
            Path to processed background video file
        """
        add_breadcrumb(
            "Starting background processing",
            category="background",
            data={
                "has_custom_background": background_path is not None,
                "style": style.value,
                "duration": duration,
                "resolution": resolution
            }
        )
        
        try:
            with SentryTransaction("process_background", "background_processing"):
                if background_path and background_path.exists():
                    return await self._process_custom_background(
                        background_path, duration, resolution
                    )
                else:
                    return await self._generate_style_background(
                        style, duration, resolution
                    )
        except Exception as e:
            capture_exception(
                e,
                context={
                    "operation": "background_processing",
                    "background_path": str(background_path) if background_path else None,
                    "style": style.value,
                    "duration": duration
                }
            )
            logger.error(f"Background processing failed: {e}")
            return None
    
    async def _process_custom_background(
        self,
        background_path: Path,
        duration: float,
        resolution: str
    ) -> Optional[Path]:
        """Process custom background file."""
        file_extension = background_path.suffix.lower().lstrip('.')
        
        if file_extension not in self.SUPPORTED_FORMATS:
            logger.warning(f"Unsupported background format: {file_extension}")
            return None
        
        output_path = self.temp_dir / f"background_{background_path.stem}_{int(duration)}s.mp4"
        
        add_breadcrumb(
            f"Processing {file_extension} background",
            category="background",
            data={"format": file_extension, "input_path": str(background_path)}
        )
        
        if file_extension in ['mp4', 'webm', 'mov', 'avi']:
            # Video background - loop to match duration
            return await self._loop_video_background(
                background_path, output_path, duration, resolution
            )
        else:
            # Animated image background (GIF, APNG, WEBP, MNG)
            return await self._loop_animated_background(
                background_path, output_path, duration, resolution
            )
    
    async def _loop_video_background(
        self,
        input_path: Path,
        output_path: Path,
        duration: float,
        resolution: str
    ) -> Optional[Path]:
        """Loop video background to match target duration."""
        cmd = [
            'ffmpeg', '-y',
            '-stream_loop', '-1',  # Loop indefinitely
            '-i', str(input_path),
            '-t', str(duration),  # Limit to target duration
            '-vf', f'scale={resolution}:force_original_aspect_ratio=decrease,pad={resolution}:(ow-iw)/2:(oh-ih)/2',
            '-c:v', 'libx264',
            '-preset', 'fast',  # Faster encoding
            '-crf', '23',  # Good quality/size balance
            '-pix_fmt', 'yuv420p',
            '-an',  # No audio
            str(output_path)
        ]
        
        return await self._run_ffmpeg_command(cmd, output_path)
    
    async def _loop_animated_background(
        self,
        input_path: Path,
        output_path: Path,
        duration: float,
        resolution: str
    ) -> Optional[Path]:
        """Convert and loop animated image background."""
        cmd = [
            'ffmpeg', '-y',
            '-stream_loop', '-1',  # Loop indefinitely
            '-i', str(input_path),
            '-t', str(duration),  # Limit to target duration
            '-vf', f'scale={resolution}:force_original_aspect_ratio=decrease,pad={resolution}:(ow-iw)/2:(oh-ih)/2',
            '-c:v', 'libx264',
            '-preset', 'fast',
            '-crf', '23',
            '-pix_fmt', 'yuv420p',
            '-r', '30',  # 30 FPS
            str(output_path)
        ]
        
        return await self._run_ffmpeg_command(cmd, output_path)
    
    async def _generate_style_background(
        self,
        style: LoFiStyle,
        duration: float,
        resolution: str
    ) -> Optional[Path]:
        """Generate style-specific background."""
        visual_config = self.STYLE_VISUALS.get(style, self.STYLE_VISUALS[LoFiStyle.CHILL])
        
        # Try to create animated background first
        background_path = await self._create_animated_background(
            visual_config, duration, resolution
        )
        
        if not background_path:
            # Fallback to simple gradient background
            background_path = await self._create_simple_background(
                visual_config, duration, resolution
            )
        
        return background_path
    
    async def _create_animated_background(
        self,
        visual_config: Dict[str, Any],
        duration: float,
        resolution: str
    ) -> Optional[Path]:
        """Create animated background based on visual configuration."""
        output_path = self.temp_dir / f"animated_bg_{hash(str(visual_config))}_{int(duration)}s.mp4"
        
        colors = visual_config['colors']
        primary_color = colors[0].lstrip('#')
        secondary_color = colors[1].lstrip('#') if len(colors) > 1 else primary_color
        
        # Create animated gradient with movement
        cmd = [
            'ffmpeg', '-y',
            '-f', 'lavfi',
            '-i', f'color=c=0x{primary_color}:size={resolution}:duration={duration}',
            '-f', 'lavfi',
            '-i', f'color=c=0x{secondary_color}:size={resolution}:duration={duration}',
            '-filter_complex',
            '[0][1]blend=all_mode=overlay:all_opacity=0.5,'
            'geq=r=\'r(X,Y)*sin(T*2*PI/10)\':g=\'g(X,Y)*cos(T*2*PI/8)\':b=\'b(X,Y)*sin(T*2*PI/12)\','
            'format=yuv420p',
            '-t', str(duration),
            '-r', '30',
            str(output_path)
        ]
        
        return await self._run_ffmpeg_command(cmd, output_path)
    
    async def _create_simple_background(
        self,
        visual_config: Dict[str, Any],
        duration: float,
        resolution: str
    ) -> Optional[Path]:
        """Create simple gradient background as fallback."""
        output_path = self.temp_dir / f"simple_bg_{hash(str(visual_config))}_{int(duration)}s.mp4"
        
        colors = visual_config['colors']
        primary_color = colors[0].lstrip('#')
        secondary_color = colors[1].lstrip('#') if len(colors) > 1 else primary_color
        
        cmd = [
            'ffmpeg', '-y',
            '-f', 'lavfi',
            '-i', f'color=c=0x{primary_color}:size={resolution}:duration={duration}',
            '-f', 'lavfi',
            '-i', f'color=c=0x{secondary_color}:size={resolution}:duration={duration}',
            '-filter_complex',
            '[0][1]blend=all_mode=overlay:all_opacity=0.5,format=yuv420p',
            '-t', str(duration),
            '-r', '30',
            str(output_path)
        ]
        
        return await self._run_ffmpeg_command(cmd, output_path)
    
    async def _run_ffmpeg_command(self, cmd: List[str], output_path: Path) -> Optional[Path]:
        """Run FFmpeg command and return output path on success."""
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and output_path.exists():
                add_breadcrumb(
                    "FFmpeg command successful",
                    category="background",
                    data={"output_size": output_path.stat().st_size}
                )
                return output_path
            else:
                logger.error(f"FFmpeg error: {stderr.decode()}")
                return None
                
        except Exception as e:
            logger.error(f"Error running FFmpeg command: {e}")
            return None
    
    def cleanup_temp_files(self) -> None:
        """Clean up temporary background files."""
        try:
            for file_path in self.temp_dir.glob("*"):
                if file_path.is_file():
                    file_path.unlink()
            add_breadcrumb("Background temp files cleaned up", category="cleanup")
        except Exception as e:
            logger.warning(f"Error cleaning up background temp files: {e}")
    
    @staticmethod
    def is_supported_format(file_path: Path) -> bool:
        """Check if file format is supported for backgrounds."""
        if not file_path.exists():
            return False
        
        extension = file_path.suffix.lower().lstrip('.')
        return extension in BackgroundProcessor.SUPPORTED_FORMATS
