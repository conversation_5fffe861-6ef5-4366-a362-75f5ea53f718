#!/usr/bin/env python3
"""
Lo-Fi Video Generator CLI

A command-line interface for generating lo-fi music videos.
"""

import asyncio
import argparse
import os
import sys
from pathlib import Path
from typing import Optional
import logging

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import Sentry configuration first
from src.sentry_config import configure_sentry, capture_exception, add_breadcrumb

# Configure Sentry early
configure_sentry()

logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s - %(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[logging.StreamHandler(), logging.FileHandler("lofi_cli.log")]
)

from src.lofi_manager import LoFiMusicManager
from src.video_generator import LoFiVideoGenerator
from src.music_sources.base import LoFiStyle


class LoFiCLI:
    """Command-line interface for lo-fi video generation."""
    
    def __init__(self):
        self.music_manager = None
        self.video_generator = None
    
    async def setup(self):
        """Initialize managers and check dependencies."""
        try:
            # Load environment variables
            from dotenv import load_dotenv
            load_dotenv()

            # Initialize music manager
            self.music_manager = LoFiMusicManager()

            # Configure music sources
            freesound_key = os.getenv("FREESOUND_API_KEY")
            if not freesound_key:
                error_msg = "FREESOUND_API_KEY not found in environment"
                logger.error(error_msg)
                logger.info("Please set your Freesound API key:")
                logger.info("export FREESOUND_API_KEY='your_key_here'")
                logger.info("Get your key at: https://freesound.org/apiv2/apply")
                return False

            self.music_manager.configure_sources(freesound_key)

            # Initialize video generator
            self.video_generator = LoFiVideoGenerator()

            # Check video dependencies
            deps_ok, missing = self.video_generator.check_dependencies()
            if not deps_ok:
                logger.error("❌ Missing dependencies:")
                for dep in missing:
                    logger.error(f"  - {dep}")
                logger.info("\nPlease install FFmpeg:")
                logger.info("  Ubuntu/Debian: sudo apt install ffmpeg")
                logger.info("  macOS: brew install ffmpeg")
                logger.info("  Windows: Download from https://ffmpeg.org/")
                return False

            return True

        except Exception as e:
            capture_exception(e, context={"operation": "cli_setup"})
            logger.error(f"❌ Setup failed: {e}")
            return False
    
    async def generate_video(self, duration: float, style: str, output: Optional[str] = None,
                           background: Optional[str] = None, resolution: str = "1920x1080"):
        """Generate a lo-fi video with specified duration and style."""

        # Parse style
        lofi_style = LoFiMusicManager.parse_style(style)
        if not lofi_style:
            logger.error(f"Invalid style: {style}")
            logger.error(f"Available styles: {', '.join(LoFiMusicManager.get_available_styles())}")
            return False

        # Validate background file if provided
        background_path = None
        if background:
            background_path = Path(background)
            if not background_path.exists():
                logger.error(f"Background file not found: {background}")
                return False

            if not self.video_generator.is_supported_background_format(background_path):
                logger.error(f"Unsupported background format: {background_path.suffix}")
                logger.error(f"Supported formats: GIF, APNG, WEBP, MNG, MP4, WebM, MOV, AVI")
                return False

            logger.info(f"Using custom background: {background}")

        add_breadcrumb("Starting video generation", category="cli", data={
            "duration": duration,
            "style": style,
            "has_background": background is not None,
            "resolution": resolution
        })

        logger.info(f"Generating {duration}s lo-fi video with {style} style...")

        try:
            # Determine if we need multiple tracks for long videos
            LONG_VIDEO_THRESHOLD = 600  # 10 minutes
            
            if duration > LONG_VIDEO_THRESHOLD:
                # Long video - use multiple tracks
                logger.info("🔍 Searching for multiple lo-fi tracks for long video...")
                tracks = await self.music_manager.get_tracks_for_long_video(duration, lofi_style)

                if not tracks:
                    logger.error(f"No suitable lo-fi tracks found for {style} style, {duration}s duration")
                    logger.error(f"Available styles: {', '.join(LoFiMusicManager.get_available_styles())}")
                    return False

                logger.info(f"Found {len(tracks)} tracks")

                # Download all audio tracks
                audio_data_list = await self.music_manager.download_multiple_tracks_audio(tracks)

                if not audio_data_list:
                    logger.error("Failed to download any audio tracks")
                    return False

                logger.info(f"Downloaded {len(audio_data_list)} audio tracks")
            
                # Generate video from multiple tracks
                logger.info("🎬 Generating video from multiple tracks...")
                video_path = await self.video_generator.generate_video_from_multiple_tracks(
                    tracks, audio_data_list, duration, lofi_style, output, background_path, resolution
                )

                # Set up variables for attribution display
                used_tracks = tracks
                
            else:
                # Short video - use single track
                logger.info("🔍 Searching for lo-fi music...")
                track = await self.music_manager.get_track_for_video(duration, lofi_style)

                if not track:
                    logger.error(f"No suitable lo-fi tracks found for {style} style, {duration}s duration")
                    logger.error(f"Available styles: {', '.join(LoFiMusicManager.get_available_styles())}")
                    return False

                logger.info(f"Found track: '{track.title}' by {track.artist}")
                logger.info(f"   Duration: {track.duration:.1f}s | License: {track.license_type.value}")
                logger.info(f"   Source: {track.source}")

                # Download audio
                logger.info("⬇️  Downloading audio...")
                audio_data = await self.music_manager.download_track_audio(track)

                if not audio_data:
                    logger.error(f"Failed to download audio for track {track.id}")
                    return False

                logger.info(f"Downloaded {len(audio_data)} bytes of audio data")
            
                # Generate video
                logger.info("🎬 Generating video...")
                video_path = await self.video_generator.generate_video(
                    track, audio_data, duration, lofi_style, output, background_path, resolution
                )

                # Set up variables for attribution display
                used_tracks = [track]

            if not video_path:
                logger.error("Video generation failed")
                return False

            logger.info(f"Video generated successfully: {video_path.name}")
            logger.info(f"Output: {video_path}")

            # Show attribution if required
            if len(used_tracks) == 1:
                attribution = self.music_manager.get_attribution_text(used_tracks[0])
                if attribution:
                    logger.info("\n📜 Attribution required:")
                    for attr in attribution:
                        logger.info(f"   {attr}")
                else:
                    logger.info("\n✅ No attribution required (public domain)")
            else:
                # Multiple tracks attribution
                attribution_list = self.music_manager.get_attribution_text_for_multiple(used_tracks)
                if attribution_list:
                    logger.info(f"\n📜 Attribution required for {len(used_tracks)} tracks:")
                    for i, attr in enumerate(attribution_list, 1):
                        logger.info(f"   {i}. {attr}")
                else:
                    logger.info(f"\n✅ No attribution required for {len(used_tracks)} tracks (public domain)")

            # Show video info
            video_info = self.video_generator.get_video_info(video_path)
            if video_info:
                format_info = video_info.get('format', {})
                file_size = int(format_info.get('size', 0))
                if file_size > 0:
                    logger.info(f"📊 File size: {file_size / (1024*1024):.1f} MB")

            # Log successful generation
            if len(used_tracks) == 1:
                track = used_tracks[0]
                logger.info(f"Video generated successfully: {video_path.name}")
                logger.info(f"Track: {track.title} by {track.artist}")
                logger.info(f"Duration: {duration}s")
                logger.info(f"Style: {style}")
                logger.info(f"Output: {video_path}")
            else:
                logger.info(f"Multi-track video generated successfully: {video_path.name}")
                logger.info(f"Duration: {duration}s")
                logger.info(f"Style: {style}")
                logger.info(f"Output: {video_path}")
            return True
        except Exception as e:
            capture_exception(e, context={
                "operation": "video_generation",
                "duration": duration,
                "style": style,
                "has_background": background is not None,
                "resolution": resolution
            })
            logger.error(f"Error: {e}")
            return False

    async def list_styles(self):
        """List available lo-fi styles."""
        logger.info("🎨 Available Lo-Fi Styles:")
        logger.info("=" * 30)
        
        styles = LoFiMusicManager.get_available_styles()
        for style in styles:
            style_enum = LoFiMusicManager.parse_style(style)
            keywords = LoFiMusicManager.STYLE_KEYWORDS.get(style_enum, [])
            logger.info(f"  {style:<12} - {', '.join(keywords[:3])}")
    
    async def preview_style(self, style: str, count: int = 5):
        """Preview tracks available for a style."""
        lofi_style = LoFiMusicManager.parse_style(style)
        if not lofi_style:
            logger.error(f"Invalid style: {style}")
            return False
        
        logger.info(f"🎵 Preview of {style} lo-fi tracks:")
        logger.info("=" * 40)
        
        try:
            tracks = await self.music_manager.find_lofi_for_style(lofi_style, 180.0, limit=count)
            
            if not tracks:
                logger.error("No tracks found for this style")
                return False
            
            for i, track in enumerate(tracks, 1):
                logger.info(f"{i}. {track.title}")
                logger.info(f"   Artist: {track.artist}")
                logger.info(f"   Duration: {track.duration:.1f}s")
                logger.info(f"   Tags: {', '.join(track.tags[:5])}")
            
            return True
            
        except Exception as e:
            capture_exception(e, context={
                "operation": "preview_style",
                "style": style
            })
            logger.error(f"Error: {e}")
            return False
    
    async def cleanup(self):
        """Clean up resources."""
        if self.music_manager:
            await self.music_manager.close()
        if self.video_generator:
            self.video_generator.cleanup_temp_files()


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Generate lo-fi music videos",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s generate 120 calming                              # 2-minute calming video
  %(prog)s generate 300 upbeat -o my_video.mp4               # 5-minute upbeat video
  %(prog)s generate 180 chill -b background.gif              # Use custom GIF background
  %(prog)s generate 240 study -b loop.mp4 -r 1280x720       # Custom video background, 720p
  %(prog)s list-styles                                       # Show available styles
  %(prog)s preview chill                                     # Preview chill tracks
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Generate command
    gen_parser = subparsers.add_parser('generate', help='Generate a lo-fi video')
    gen_parser.add_argument('duration', type=float, help='Video duration in seconds')
    gen_parser.add_argument('style', help='Lo-fi style (e.g., calming, upbeat, chill)')
    gen_parser.add_argument('-o', '--output', help='Output filename')
    gen_parser.add_argument('-b', '--background', help='Custom background file (GIF, APNG, WEBP, MNG, MP4, WebM)')
    gen_parser.add_argument('-r', '--resolution', default='1920x1080',
                           choices=['1920x1080', '1280x720', '854x480'],
                           help='Video resolution (default: 1920x1080)')
    
    # List styles command
    subparsers.add_parser('list-styles', help='List available lo-fi styles')
    
    # Preview command
    preview_parser = subparsers.add_parser('preview', help='Preview tracks for a style')
    preview_parser.add_argument('style', help='Lo-fi style to preview')
    preview_parser.add_argument('-c', '--count', type=int, default=5, help='Number of tracks to show')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    async def run_cli():
        cli = LoFiCLI()
        
        try:
            if args.command == 'list-styles':
                await cli.list_styles()
                return
            
            # For other commands, we need to setup
            if not await cli.setup():
                return
            
            if args.command == 'generate':
                success = await cli.generate_video(
                    args.duration, args.style, args.output,
                    args.background, args.resolution
                )
                if not success:
                    sys.exit(1)
            
            elif args.command == 'preview':
                success = await cli.preview_style(args.style, args.count)
                if not success:
                    sys.exit(1)
        
        finally:
            await cli.cleanup()
    
    # Run the async CLI
    try:
        asyncio.run(run_cli())
    except KeyboardInterrupt:
        logger.info("\n👋 Goodbye!")
    except Exception as e:
        capture_exception(e, context={"operation": "cli_main"})
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
