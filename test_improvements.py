#!/usr/bin/env python3
"""
Test script for the improved lo-fi video generator.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.sentry_config import configure_sentry
from src.background_processor import BackgroundProcessor
from src.audio_processor import AudioProcessor
from src.video_composer import VideoComposer
from src.video_generator import LoFiVideoGenerator
from src.music_sources.base import LoFiStyle

async def test_sentry_config():
    """Test Sentry configuration."""
    print("Testing Sentry configuration...")
    
    # Test without DSN (should not initialize)
    result = configure_sentry()
    print(f"Sentry init without DSN: {result}")
    
    # Test with debug mode
    result = configure_sentry(debug=True)
    print(f"Sentry init with debug: {result}")
    
    print("✅ Sentry configuration test completed")

async def test_background_processor():
    """Test background processor."""
    print("\nTesting background processor...")
    
    processor = BackgroundProcessor()
    
    # Test style background generation
    background_path = await processor.process_background(
        None, LoFiStyle.CHILL, 10.0, "1920x1080"
    )
    
    if background_path and background_path.exists():
        print(f"✅ Generated background: {background_path}")
        print(f"   File size: {background_path.stat().st_size} bytes")
    else:
        print("❌ Failed to generate background")
    
    # Test supported format check
    test_formats = [
        Path("test.gif"),
        Path("test.mp4"),
        Path("test.webp"),
        Path("test.txt")  # Unsupported
    ]
    
    for test_file in test_formats:
        supported = BackgroundProcessor.is_supported_format(test_file)
        print(f"   {test_file.suffix}: {'✅' if supported else '❌'}")
    
    processor.cleanup_temp_files()
    print("✅ Background processor test completed")

async def test_audio_processor():
    """Test audio processor."""
    print("\nTesting audio processor...")
    
    processor = AudioProcessor()
    
    # Create dummy audio data
    dummy_audio = b"dummy audio data for testing"
    
    from src.music_sources.base import Track, LicenseType
    
    test_track = Track(
        id="test_123",
        title="Test Track",
        artist="Test Artist",
        duration=30.0,
        license_type=LicenseType.CC0,
        source="test",
        tags=["test", "lofi"]
    )
    
    # Note: This will fail with real FFmpeg since we're using dummy data
    # but it tests the code structure
    try:
        result = await processor.process_single_audio(dummy_audio, test_track, 10.0)
        if result:
            print(f"✅ Audio processing structure works: {result}")
        else:
            print("⚠️  Audio processing failed (expected with dummy data)")
    except Exception as e:
        print(f"⚠️  Audio processing error (expected): {e}")
    
    processor.cleanup_temp_files()
    print("✅ Audio processor test completed")

async def test_video_composer():
    """Test video composer."""
    print("\nTesting video composer...")
    
    composer = VideoComposer()
    
    # Test hardware acceleration detection
    hw_accel = await composer._get_hardware_acceleration()
    print(f"   Hardware acceleration: {hw_accel or 'None'}")
    
    # Test encoding time estimation
    estimated_time = await composer.estimate_encoding_time(60.0, "1920x1080")
    print(f"   Estimated encoding time for 60s video: {estimated_time:.2f}s")
    
    composer.cleanup_temp_files()
    print("✅ Video composer test completed")

async def test_video_generator():
    """Test video generator."""
    print("\nTesting video generator...")
    
    generator = LoFiVideoGenerator()
    
    # Test format support
    test_files = [
        Path("test.gif"),
        Path("test.mp4"),
        Path("test.webp"),
        Path("test.txt")
    ]
    
    for test_file in test_files:
        supported = generator.is_supported_background_format(test_file)
        print(f"   {test_file.suffix}: {'✅' if supported else '❌'}")
    
    # Test generation time estimation
    estimated_time = await generator.estimate_generation_time(120.0, "1920x1080")
    print(f"   Estimated generation time for 120s video: {estimated_time:.2f}s")
    
    generator.cleanup_temp_files()
    print("✅ Video generator test completed")

async def main():
    """Run all tests."""
    print("🧪 Testing Lo-Fi Video Generator Improvements")
    print("=" * 50)
    
    try:
        await test_sentry_config()
        await test_background_processor()
        await test_audio_processor()
        await test_video_composer()
        await test_video_generator()
        
        print("\n🎉 All tests completed!")
        print("\nNew features available:")
        print("✅ Sentry error tracking and performance monitoring")
        print("✅ Parallel audio and background processing")
        print("✅ Hardware-accelerated video encoding")
        print("✅ Support for custom backgrounds (GIF, APNG, WEBP, MNG, MP4, WebM)")
        print("✅ Multiple resolution options")
        print("✅ Improved error handling and logging")
        print("✅ Modular, organized code structure")
        
        print("\nUsage examples:")
        print("python3 lofi_cli.py generate 120 chill")
        print("python3 lofi_cli.py generate 180 upbeat -b background.gif")
        print("python3 lofi_cli.py generate 240 study -b loop.mp4 -r 1280x720")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
